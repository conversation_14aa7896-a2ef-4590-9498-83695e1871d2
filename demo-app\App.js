import React, { useState } from 'react';
import { <PERSON><PERSON>reaView, ScrollView, View, Text, StyleSheet, Switch } from 'react-native';
import { ThemeProvider, useTheme, Button, OTPInput } from '../src';

const DemoSection = ({ title, children }) => {
  const { theme } = useTheme();

  return (
    <View style={[styles.section, { borderColor: theme.borderColor }]}>
      <Text style={[styles.sectionTitle, { color: theme.textColor, backgroundColor: theme.borderColor }]}>{title}</Text>
      <View style={styles.sectionContent}>
        {children}
      </View>
    </View>
  );
};

const ThemeToggle = () => {
  const { theme, colorMode, toggleTheme } = useTheme();

  return (
    <View style={styles.themeToggle}>
      <Text style={{ color: theme.textColor }}>Dark Mode</Text>
      <Switch
        value={colorMode === 'dark'}
        onValueChange={toggleTheme}
      />
    </View>
  );
};

const ButtonDemo = () => {
  return (
    <DemoSection title="Button">
      <View style={styles.buttonRow}>
        <Button 
          title="Filled Button" 
          onPress={() => alert('Filled Button Pressed')} 
        />
      </View>
      
      <View style={styles.buttonRow}>
        <Button 
          title="Outlined" 
          variant="outlined" 
          onPress={() => alert('Outlined Button Pressed')} 
        />
      </View>
      
      <View style={styles.buttonRow}>
        <Button 
          title="Text Button" 
          variant="text" 
          onPress={() => alert('Text Button Pressed')} 
        />
      </View>
    </DemoSection>
  );
};

const OTPInputDemo = () => {
  const { theme } = useTheme();
  const [otpValue, setOtpValue] = useState('');

  return (
    <DemoSection title="OTP Input">
      <Text style={[styles.label, { color: theme.textColor }]}>Standard OTP (4 digits)</Text>
      <OTPInput
        length={4}
        value={otpValue}
        onChange={setOtpValue}
      />
      <Text style={[styles.valueText, { color: theme.placeholderColor }]}>Value: {otpValue}</Text>
    </DemoSection>
  );
};

const App = () => {
  return (
    <ThemeProvider>
      <AppContent />
    </ThemeProvider>
  );
};

const AppContent = () => {
  const { theme } = useTheme();

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.backgroundColor }]}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <Text style={[styles.title, { color: theme.textColor }]}>PrimeNative Demo</Text>
        <ThemeToggle />

        <ButtonDemo />
        <OTPInputDemo />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  section: {
    marginBottom: 24,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    overflow: 'hidden',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    padding: 12,
    backgroundColor: '#f5f5f5',
  },
  sectionContent: {
    padding: 16,
  },
  buttonRow: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  themeToggle: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  label: {
    marginBottom: 8,
    fontSize: 14,
  },
  valueText: {
    marginTop: 8,
    fontSize: 12,
    color: '#666',
  },
  spacer: {
    height: 24,
  },
});

export default App;