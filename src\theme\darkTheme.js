// Dark theme
export default {
  // Colors
  primaryColor: '#90CAF9',
  secondaryColor: '#81D4FA',
  backgroundColor: '#121212',
  textColor: '#FFFFFF',
  placeholderColor: '#9E9E9E',
  borderColor: '#424242',
  errorColor: '#EF9A9A',
  successColor: '#A5D6A7',
  warningColor: '#FFE082',
  infoColor: '#90CAF9',
  disabledColor: '#424242',
  disabledTextColor: '#757575',
  
  // Typography
  fontFamily: 'System',
  fontSize: {
    small: 12,
    medium: 14,
    large: 16,
    xlarge: 18,
  },
  fontWeight: {
    light: '300',
    regular: '400',
    medium: '500',
    bold: '700',
  },
  
  // Spacing
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
  },
  
  // Border radius
  borderRadius: {
    small: 4,
    medium: 8,
    large: 12,
    round: 999,
  },
  
  // Shadows
  shadow: {
    small: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.4,
      shadowRadius: 1.41,
      elevation: 2,
    },
    medium: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.43,
      shadowRadius: 2.62,
      elevation: 4,
    },
    large: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.5,
      shadowRadius: 4.65,
      elevation: 8,
    },
  },
};