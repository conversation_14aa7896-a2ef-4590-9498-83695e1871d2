# PrimeNative

A JavaScript-only UI component library for React Native applications. PrimeNative provides reusable, customizable, and accessible components that work across iOS and Android platforms.

## Features

- Pure JavaScript implementation (no TypeScript)
- Themeable components via React Context
- Customizable via props
- Minimal dependencies
- Cross-platform support
- Accessibility features

## Installation

```bash
npm install primenative
# or
yarn add primenative
```

## Usage

Wrap your application with the `ThemeProvider`:

```javascript
import React from 'react';
import { ThemeProvider } from 'primenative';
import App from './App';

export default function Root() {
  return (
    <ThemeProvider>
      <App />
    </ThemeProvider>
  );
}
```

Then use components in your app:

```javascript
import React from 'react';
import { View } from 'react-native';
import { Button, OTPInput } from 'primenative';

const MyScreen = () => {
  const [otp, setOtp] = useState('');
  
  return (
    <View>
      <Button 
        title="Press Me" 
        onPress={() => alert('Button pressed!')} 
      />
      
      <OTPInput
        length={4}
        value={otp}
        onChange={setOtp}
      />
    </View>
  );
};
```

## Components

### Button

A customizable button component with different variants and states.

```javascript
import { Button } from 'primenative';

// Basic usage
<Button 
  title="Press Me" 
  onPress={() => alert('Button pressed!')} 
/>

// Variants
<Button title="Filled" variant="filled" onPress={() => {}} />
<Button title="Outlined" variant="outlined" onPress={() => {}} />
<Button title="Text" variant="text" onPress={() => {}} />

// Sizes
<Button title="Small" size="small" onPress={() => {}} />
<Button title="Medium" size="medium" onPress={() => {}} />
<Button title="Large" size="large" onPress={() => {}} />

// States
<Button title="Disabled" disabled onPress={() => {}} />
<Button title="Loading" loading onPress={() => {}} />
```

### OTPInput

A flexible OTP input component with automatic focus switching.

```javascript
import { OTPInput } from 'primenative';

const [otp, setOtp] = useState('');

// Basic usage
<OTPInput
  length={4}
  value={otp}
  onChange={setOtp}
/>

// Secure input (masked)
<OTPInput
  length={6}
  value={otp}
  onChange={setOtp}
  secure
/>

// Custom styling
<OTPInput
  length={4}
  value={otp}
  onChange={setOtp}
  cellStyle={{ borderRadius: 8 }}
  cellTextStyle={{ fontWeight: 'bold' }}
/>
```

## Theming

PrimeNative provides a theming system that allows you to customize the appearance of all components.

```javascript
import React from 'react';
import { ThemeProvider } from 'primenative';

// Custom theme
const myTheme = {
  primaryColor: '#FF5722',
  secondaryColor: '#FFC107',
  // ... other theme properties
};

export default function Root() {
  return (
    <ThemeProvider theme={myTheme}>
      <App />
    </ThemeProvider>
  );
}
```

You can also access the theme in your components:

```javascript
import { useTheme } from 'primenative';

const MyComponent = () => {
  const { theme, colorMode, toggleTheme } = useTheme();
  
  return (
    <View style={{ backgroundColor: theme.backgroundColor }}>
      {/* Your component content */}
    </View>
  );
};
```

## Local Development

To develop and test this library locally:

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   # or
   yarn
   ```
3. Link the library to your test app:
   ```bash
   # In the library directory
   npm link
   
   # In your test app directory
   npm link primenative
   ```
4. Run the demo app:
   ```bash
   cd demo
   npm install
   npm start
   ```

## License

MIT