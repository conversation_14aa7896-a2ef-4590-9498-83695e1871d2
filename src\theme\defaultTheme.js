// Light theme (default)
export default {
  // Colors
  primaryColor: '#2196F3',
  secondaryColor: '#03A9F4',
  backgroundColor: '#FFFFFF',
  textColor: '#212121',
  placeholderColor: '#9E9E9E',
  borderColor: '#E0E0E0',
  errorColor: '#F44336',
  successColor: '#4CAF50',
  warningColor: '#FFC107',
  infoColor: '#2196F3',
  disabledColor: '#BDBDBD',
  disabledTextColor: '#757575',
  
  // Typography
  fontFamily: 'System',
  fontSize: {
    small: 12,
    medium: 14,
    large: 16,
    xlarge: 18,
  },
  fontWeight: {
    light: '300',
    regular: '400',
    medium: '500',
    bold: '700',
  },
  
  // Spacing
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
  },
  
  // Border radius
  borderRadius: {
    small: 4,
    medium: 8,
    large: 12,
    round: 999,
  },
  
  // Shadows
  shadow: {
    small: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.2,
      shadowRadius: 1.41,
      elevation: 2,
    },
    medium: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.23,
      shadowRadius: 2.62,
      elevation: 4,
    },
    large: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 4.65,
      elevation: 8,
    },
  },
};