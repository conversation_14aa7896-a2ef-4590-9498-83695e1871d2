{"name": "primenative", "version": "0.1.0", "description": "A JavaScript-only UI component library for React Native", "main": "src/index.js", "scripts": {"test": "jest", "lint": "eslint ."}, "keywords": ["react-native", "ui", "components", "mobile", "ios", "android"], "author": "", "license": "MIT", "peerDependencies": {"react": ">=16.8.0", "react-native": ">=0.60.0"}, "devDependencies": {"@babel/core": "^7.15.0", "@babel/runtime": "^7.15.3", "@react-native-community/eslint-config": "^3.0.0", "@testing-library/react-native": "^7.2.0", "babel-jest": "^27.0.6", "eslint": "^7.32.0", "jest": "^27.0.6", "metro-react-native-babel-preset": "^0.66.2", "prop-types": "^15.7.2", "react": "17.0.2", "react-native": "0.65.1", "react-test-renderer": "17.0.2"}, "jest": {"preset": "react-native"}, "dependencies": {"expo-image": "^2.3.0", "expo-linking": "^7.1.5"}}