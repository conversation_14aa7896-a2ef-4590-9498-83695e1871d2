import React, { createContext, useContext, useState } from 'react';
import { useColorScheme } from 'react-native';
import defaultTheme from './defaultTheme';
import darkTheme from './darkTheme';

const ThemeContext = createContext();

export const ThemeProvider = ({ theme, children }) => {
  const deviceColorScheme = useColorScheme();
  const [colorMode, setColorMode] = useState(deviceColorScheme || 'light');
  
  // Use provided theme or fallback to default themes
  const baseTheme = theme || (colorMode === 'dark' ? darkTheme : defaultTheme);
  
  const toggleTheme = () => {
    setColorMode(prev => prev === 'light' ? 'dark' : 'light');
  };
  
  const value = {
    theme: baseTheme,
    colorMode,
    setColorMode,
    toggleTheme
  };
  
  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};