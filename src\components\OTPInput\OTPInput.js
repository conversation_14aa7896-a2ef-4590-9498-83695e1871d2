import React, { useState, useRef, useEffect } from 'react';
import { View, TextInput, StyleSheet, Keyboard } from 'react-native';
import PropTypes from 'prop-types';
import { useTheme } from '../../theme';

const OTPInput = ({
  length = 4,
  value = '',
  onChange,
  autoFocus = true,
  secure = false,
  keyboardType = 'numeric',
  cellStyle,
  cellTextStyle,
  containerStyle,
  cellFocusStyle,
  cellFilledStyle,
  cellErrorStyle,
  isError = false,
  disabled = false,
  testID,
}) => {
  const { theme } = useTheme();
  const [localValue, setLocalValue] = useState(value.split(''));
  const [focusedIndex, setFocusedIndex] = useState(autoFocus ? 0 : -1);
  const inputRefs = useRef([]);
  
  // Initialize input refs
  useEffect(() => {
    inputRefs.current = Array(length).fill().map((_, i) => inputRefs.current[i] || React.createRef());
  }, [length]);
  
  // Update local value when prop value changes
  useEffect(() => {
    setLocalValue(value.split(''));
  }, [value]);
  
  // Focus on first input when autoFocus is true
  useEffect(() => {
    if (autoFocus && inputRefs.current[0]) {
      setTimeout(() => {
        inputRefs.current[0].focus();
      }, 100);
    }
  }, [autoFocus]);
  
  const handleChange = (text, index) => {
    // Only allow one character per cell
    if (text.length > 1) {
      text = text.charAt(text.length - 1);
    }
    
    // Update the value
    const newValue = [...localValue];
    newValue[index] = text;
    setLocalValue(newValue);
    
    // Call onChange with the new combined value
    if (onChange) {
      onChange(newValue.join(''));
    }
    
    // Auto-advance to next input if text was entered
    if (text && index < length - 1) {
      inputRefs.current[index + 1].focus();
    }
  };
  
  const handleKeyPress = (e, index) => {
    // Handle backspace
    if (e.nativeEvent.key === 'Backspace') {
      if (!localValue[index] && index > 0) {
        // If current cell is empty and backspace is pressed, move to previous cell
        const newValue = [...localValue];
        newValue[index - 1] = '';
        setLocalValue(newValue);
        
        if (onChange) {
          onChange(newValue.join(''));
        }
        
        inputRefs.current[index - 1].focus();
      }
    }
  };
  
  const handleFocus = (index) => {
    setFocusedIndex(index);
  };
  
  const handleBlur = () => {
    setFocusedIndex(-1);
  };
  
  const getCellStyle = (index) => {
    const isFocused = focusedIndex === index;
    const isFilled = !!localValue[index];
    
    return [
      styles.cell,
      {
        borderColor: theme.borderColor,
        borderRadius: theme.borderRadius.small,
        backgroundColor: theme.backgroundColor,
      },
      isFocused && { ...styles.cellFocused, borderColor: theme.primaryColor, ...cellFocusStyle },
      isFilled && { ...styles.cellFilled, ...cellFilledStyle },
      isError && { ...styles.cellError, borderColor: theme.errorColor, ...cellErrorStyle },
      cellStyle,
    ];
  };
  
  const getCellTextStyle = () => {
    return [
      styles.cellText,
      {
        color: theme.textColor,
        fontSize: theme.fontSize.large,
        fontFamily: theme.fontFamily,
      },
      cellTextStyle,
    ];
  };
  
  const renderCells = () => {
    const cells = [];
    
    for (let i = 0; i < length; i++) {
      cells.push(
        <TextInput
          key={i}
          ref={(ref) => (inputRefs.current[i] = ref)}
          style={[getCellStyle(i), getCellTextStyle()]}
          value={localValue[i] || ''}
          onChangeText={(text) => handleChange(text, i)}
          onKeyPress={(e) => handleKeyPress(e, i)}
          onFocus={() => handleFocus(i)}
          onBlur={handleBlur}
          keyboardType={keyboardType}
          secureTextEntry={secure}
          maxLength={1}
          selectTextOnFocus
          editable={!disabled}
          testID={`${testID}-input-${i}`}
          accessibilityLabel={`OTP digit ${i + 1} of ${length}`}
          accessibilityHint={`Enter digit ${i + 1} of your verification code`}
        />
      );
    }
    
    return cells;
  };
  
  return (
    <View 
      style={[styles.container, containerStyle]}
      testID={testID}
    >
      {renderCells()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  cell: {
    width: 45,
    height: 50,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
    textAlign: 'center',
    marginHorizontal: 5,
  },
  cellText: {
    textAlign: 'center',
  },
  cellFocused: {
    borderWidth: 2,
  },
  cellFilled: {},
  cellError: {
    borderWidth: 2,
  },
});

OTPInput.propTypes = {
  length: PropTypes.number,
  value: PropTypes.string,
  onChange: PropTypes.func,
  autoFocus: PropTypes.bool,
  secure: PropTypes.bool,
  keyboardType: PropTypes.string,
  cellStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
  cellTextStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
  containerStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
  cellFocusStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
  cellFilledStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
  cellErrorStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
  isError: PropTypes.bool,
  disabled: PropTypes.bool,
  testID: PropTypes.string,
};

export default OTPInput;